package com.yhl.scp.mps.domain.algorithm;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.yhl.aps.algorithm.modules.OperationSortModule;
import com.yhl.aps.algorithm.modules.Solver4Rzz;
import com.yhl.aps.api.capacity.TimeSlotWithEfficiency;
import com.yhl.aps.api.factory.BatchLimitRuleFactory;
import com.yhl.aps.api.obj.Spec;
import com.yhl.aps.api.obj.*;
import com.yhl.aps.api.runner.APSInput;
import com.yhl.aps.kernel.obj.*;
import com.yhl.aps.kernel.rules.BatchLimitRule;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ams.basic.enums.ScheduleTypeEnum;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.basic.enums.AssignQtyTypeEnum;
import com.yhl.scp.mds.basic.resource.enums.ResourceCategoryEnum;
import com.yhl.scp.mds.basic.resource.enums.ResourceTypeEnum;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.other.vo.GlobalConfigsVO;
import com.yhl.scp.mds.extension.param.vo.ProductionSchedulingParamVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mps.algorithm.schedule.input.*;
import com.yhl.scp.mps.coating.vo.MpsCoatingChangeTimeVO;
import com.yhl.scp.mps.dispatch.ams.input.OccupyTime;
import com.yhl.scp.mps.dispatch.ams.input.Schedule;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.productionCapacity.vo.LoadingPositionCapacityVO;
import com.yhl.scp.mps.rule.enums.AlgorithmConstraintRuleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>RzzBaseAlgorithmDataConfigService</code>
 * <p>
 * 抽取AMS设置参数相关代码
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-29 11:06:15
 */
@Slf4j
public abstract class RzzBaseAlgorithmDataConfigService {

    @javax.annotation.Resource
    protected IpsNewFeign ipsNewFeign;
    @javax.annotation.Resource
    protected IpsFeign ipsFeign;
    @javax.annotation.Resource
    private MdsFeign mdsFeign;
    @javax.annotation.Resource
    private NewMdsFeign newMdsFeign;
    @javax.annotation.Resource
    private OperationTaskExtDao operationTaskExtDao;

    private static LocalDateTime convertDateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
    }

    protected static JSONObject getSolver4Rule(RzzBaseAlgorithmData baseAlgorithmData) {
        Solver4Rzz solver4Rzz = new Solver4Rzz();
        JSONObject jsonObject = new JSONObject();
        //JSONObject jsonObject = (JSONObject) JSONObject.toJSON(solver4Rzz);

        //PostProcessForReduceInventory postProcessForReduceInventory = new PostProcessForReduceInventory();
        Set<String> tags = new HashSet<>();
        tags.add("FORMING_PROCESS");
        //postProcessForReduceInventory.setTags(tags);
        // IOperationAssignHelper operationAssignHelper = new OperationAssignHelperWithSplit(postProcessForReduceInventory);

        // solver4Rzz.setOperationAssignHelper(operationAssignHelper);
        // 创建 solver4RuiZhiZe 配置的 JSONObject
        JSONObject solver4RuiZhiZe = new JSONObject();

        // 创建 assignHelper 配置的 JSONObject
        JSONObject assignHelper = new JSONObject();

        // 创建 withSplit 配置的 JSONObject
        JSONObject withSplit = new JSONObject();
        JSONObject preSplit = new JSONObject();
        JSONObject rzzPreSplitForSchedule = new JSONObject();
        // 待烘弯炉资源得id集合，遍历全工序的候选主资源判断是否是hw，是的话构建到这里
        rzzPreSplitForSchedule.put("dryBendingResourceIds", CollectionUtils.isEmpty(baseAlgorithmData.getDryBendingResourceIds())
                ? Lists.newArrayList() : baseAlgorithmData.getDryBendingResourceIds());
        preSplit.put("rzzPreSplitForSchedule", rzzPreSplitForSchedule);
        withSplit.put("preSplit", preSplit);

        // 创建 postProcessors 配置的 JSONObject
        JSONObject postProcessors = new JSONObject();

        // 创建 Processors 配置的 JSONObject
        JSONObject processors = new JSONObject();

        // 创建 strategies 配置的 JSONArray
        JSONArray strategies = new JSONArray();

        // 创建 reduceInventory 配置的 JSONObject
        JSONObject reduceInventory = new JSONObject();
        JSONObject reduce = new JSONObject();
        reduce.put("tags", tags);
        reduceInventory.put("reduceInventory", reduce);
        strategies.add(reduceInventory);

        // 创建 makeEffective 配置的 JSONObject
        JSONObject makeEffective = new JSONObject();
        makeEffective.put("makeEffective", new JSONObject());
        strategies.add(makeEffective);

        // 将 strategies 添加到 processors
        processors.put("strategies", strategies);

        // 将 processors 添加到 postProcessors
        postProcessors.put("Processors", processors);

        // 将 postProcessors 添加到 withSplit
        //withSplit.put("postProcessors", postProcessors);
        // 将 withSplit 添加到 assignHelper
        assignHelper.put("rzzWithSplit", withSplit);

        // 将 assignHelper 添加到 solver4RuiZhiZe
        solver4RuiZhiZe.put("assignHelper", assignHelper);
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_9.getCode())) {
            //爬坡
            JSONArray rampUpRules = new JSONArray();
            //装车位置（天窗）
            JSONObject loadingPositionJson = new JSONObject();
            loadingPositionJson.put("time", "02:00:00");
            loadingPositionJson.put("tags", Arrays.asList("单_双银_天窗"));
            rampUpRules.add(loadingPositionJson);
            //镀系（单/双银）
            JSONObject filmJson = new JSONObject();
            filmJson.put("time", "72:00:00");
            filmJson.put("tags", Arrays.asList("单_双银", "单_双银_天窗"));
            rampUpRules.add(filmJson);
            JSONObject rampUpJson = new JSONObject();
            rampUpJson.put("rules", rampUpRules);
            //镀膜工序规则 : 维保结束后前X天做膜系为单/双银的产品后续可以正常生产三银的产品，维保后前2小时需先生产装车位置为天窗的产品、再生产前挡的产品
            solver4RuiZhiZe.put("rampUp", rampUpJson);
        }


        jsonObject.put(Solver4Rzz.ClassName(), solver4RuiZhiZe);
        return jsonObject;
    }

    protected static JSONObject getRzzSortOperationModule(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject result = new JSONObject();
        JSONObject emptyJson = new JSONObject();
        result.put(OperationSortModule.ClassName(), emptyJson);
        return result;
    }

    public APSInput getApsInput(RzzBaseAlgorithmData baseAlgorithmData, String exeFilePath) {
        APSInput apsInput = new APSInput();
        apsInput.setProducts(getProductVec(baseAlgorithmData));
        apsInput.setResources(getResourceVec(baseAlgorithmData));
        apsInput.setOperations(getOperationVec(baseAlgorithmData, apsInput));
        //更新资源占位下子工序, 避免资源占位工序与工序中工序的内存地址不一样,导致算法报错
        apsInput.setResources(updateResourceVec(apsInput));
        apsInput.setWorkOrders(getWorkOrderVec(baseAlgorithmData, apsInput));
        apsInput.setCwd(exeFilePath);
        return apsInput;
    }

    public String getOs(String id) {
        String osName = System.getProperty("os.name").toLowerCase();
        log.info("os：{}", osName);
        if (osName.contains("win")) {
            return "D:/ams/handle" + id;
        } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
            return "/usr/local/aps/workspace/" + id;
        } else if (osName.contains("mac")) {
            return "";
        } else {
            return null;
        }
    }

    private List<Product> getProductVec(RzzBaseAlgorithmData baseAlgorithmData) {
        List<Product> pv = new ArrayList<>();
        List<RzzProductInSockingPointData> productInSockingPoints = baseAlgorithmData.getProductInSockingPoints();
        for (RzzProductInSockingPointData productInSockingPoint : productInSockingPoints) {
            Product product = new Product();
            product.setId(productInSockingPoint.getProductId());
            product.setStockingPointId(productInSockingPoint.getStockingPointId());
            product.setSeries(productInSockingPoint.getProductSeries());
            if (StrUtil.isNotEmpty(productInSockingPoint.getToolModel())) {
                product.setSeries(productInSockingPoint.getToolModel());
            }
            product.setCode(productInSockingPoint.getProductCode());
            // product.setPriority(productInSockingPoint.getPriority());
            product.setPriority(1);
            if (ProductTypeEnum.FG.getCode().equals(productInSockingPoint.getType())) {
                product.setType(Product.Type.FinishedProduct);
            } else if (ProductTypeEnum.SA.getCode().equals(productInSockingPoint.getType())) {
                product.setType(Product.Type.SemiFinishedProduct);
            } else if (ProductTypeEnum.P.getCode().equals(productInSockingPoint.getType())) {
                product.setType(Product.Type.RawMaterial);
            } else {
                product.setType(Product.Type.RawMaterial);
                // throw new BusinessException("产品类型错误");
            }
            Integer moldQuantityLimit = productInSockingPoint.getMoldQuantityLimit();
            // 模具数量限制规格
            if (null != moldQuantityLimit) {
                JsonSpec specKey = new JsonSpec();
                specKey.setName("MOLD_QUANTITY_LIMIT");
                specKey.setKey("MOLD_QUANTITY_LIMIT");
                specKey.setValue(new JsonPrimitive(moldQuantityLimit.doubleValue()));
                product.setSpecs(ListUtil.of(specKey));
            }
            pv.add(product);
        }
        return pv;

    }

    private List<com.yhl.aps.api.obj.Resource> getResourceVec(RzzBaseAlgorithmData baseAlgorithmData) {
        List<CollectionValueVO> yztResource = ipsFeign.getByCollectionCode("YZT_RESOURCE");
        List<String> yztResourceList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(yztResource)) {
            yztResourceList.addAll(yztResource.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList()));
        }
        List<com.yhl.aps.api.obj.Resource> vec = new ArrayList<>();
        List<String> yzPhysicalResourceIds = new ArrayList<>();
        for (Map.Entry<String, List<RzzResourceData>> stringListEntry : baseAlgorithmData.getResourceMap().entrySet()) {
            for (RzzResourceData resourceData : stringListEntry.getValue()) {
                com.yhl.aps.api.obj.Resource.ResourceCategory singleResourceType;
                if (stringListEntry.getKey().equals(RzzBaseAlgorithmData.RESOURCE_TYPE_TRANS_MAP.get(ResourceTypeEnum.SINGLE.name()))) {
                    singleResourceType = com.yhl.aps.api.obj.Resource.ResourceCategory.SingleResourceType;
                } else if (stringListEntry.getKey().equals(RzzBaseAlgorithmData.RESOURCE_TYPE_TRANS_MAP.get(ResourceTypeEnum.FURNACE.name()))) {
                    singleResourceType = com.yhl.aps.api.obj.Resource.ResourceCategory.FurnaceResourceType;
                } else if (stringListEntry.getKey().equals(RzzBaseAlgorithmData.RESOURCE_TYPE_TRANS_MAP.get(ResourceTypeEnum.MULTIPLE.name()))) {
                    singleResourceType = com.yhl.aps.api.obj.Resource.ResourceCategory.MultipleResourceType;
                } else {
                    singleResourceType = com.yhl.aps.api.obj.Resource.ResourceCategory.ContinuousResourceType;
                }
                com.yhl.aps.api.obj.Resource resource = com.yhl.aps.api.obj.Resource.create(singleResourceType);
                String resourceCode = resourceData.getResourceCode();
                if (stringListEntry.getKey().equals(RzzBaseAlgorithmData.RESOURCE_TYPE_TRANS_MAP.get(ResourceTypeEnum.MULTIPLE.name()))) {
                    if (resourceCode.toUpperCase().contains("HW") && ScheduleTypeEnum.RULE_AUTO.getCode().equals(baseAlgorithmData.getScheduleType())) {
                        // 识别烘弯增加配置
                        JsonArray jsonElements = new JsonArray();
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.add("productCanNotBeBatched", new JsonObject());
                        jsonElements.add(jsonObject);
                        List<BatchLimitRule> batchLimitRules = BatchLimitRuleFactory.createObjects(jsonElements);
                        resource.setRules(batchLimitRules);
                    }
                }
                if(yztResourceList.contains(resourceCode)){
                    yzPhysicalResourceIds.add(resourceData.getResourceId());
                }
                resource.setId(resourceData.getResourceId());
                resource.setGroup(resourceData.getGroupId());
                resource.setMaxBatchSize(resourceData.getMaxBatchSize());
                if (StringUtils.isNotEmpty(resourceData.getOverlapType())) {
                    com.yhl.aps.api.obj.Resource.OverlapType overlapType;
                    if (AssignQtyTypeEnum.TASK_NUM_LIMIT.getCode().equals(resourceData.getOverlapType())) {
                        overlapType = com.yhl.aps.api.obj.Resource.OverlapType.OverlapByOperation;
                    } else {
                        overlapType = com.yhl.aps.api.obj.Resource.OverlapType.OverlapByQuantity;
                    }
                    resource.setType(overlapType);
                }
                Set<ScheduleType> scheduleTypeSet = new HashSet<>();
                for (String subTaskType : resourceData.getSubTaskTypes()) {
                    if ("setupSchedule".equals(subTaskType)) {
                        scheduleTypeSet.add(ScheduleType.SetupTask);
                    } else if ("cleanupSchedule".equals(subTaskType)) {
                        scheduleTypeSet.add(ScheduleType.CleanupTask);
                    } else if ("lockSchedule".equals(subTaskType)) {

                    } else if ("processSchedule".equals(subTaskType)) {
                        scheduleTypeSet.add(ScheduleType.ProcessTask);
                    }
                }

                if (ResourceCategoryEnum.MAIN.getCode().equals(resourceData.getResourceType())) {
                    resource.setResourceType(ResourceType.MainResource);
                } else {
                    resource.setResourceType(ResourceType.ToolResource);
                }

                resource.setSupportedSubTasks(scheduleTypeSet);
                resource.setAvailable(true);
                //是否可变工时
                if (ObjUtil.isNotNull(resourceData.getVariableWorkingHours())) {
                    resource.setVariableWorkingHours(resourceData.getVariableWorkingHours());
                }
                //生产线
                if (ObjUtil.isNotNull(resourceData.getLine())) {
                    resource.setLine(resourceData.getLine());
                }
                //是否无限能力
                if (ObjUtil.isNotNull(resourceData.getInfinity())) {
                    resource.setInfinity(resourceData.getInfinity());
                }
                //是否严格遵守生产线
                if (ObjUtil.isNotNull(resourceData.getObserveLineRules())) {
                    resource.setObserveLineRules(resourceData.getObserveLineRules());
                }
                //前缓冲时间
                String bufferTimeBefore = resourceData.getBufferTimeBefore();
                Duration durationBufferTimeBefore = Duration.ofSeconds(ObjUtil.isNull(bufferTimeBefore) ? 0 : Integer.parseInt(bufferTimeBefore));
                resource.setFrontBufferDur(durationBufferTimeBefore);
                //后缓冲时间
                String bufferTimeAfter = resourceData.getBufferTimeAfter();
                Duration durationBufferTimeAfter = Duration.ofSeconds(ObjUtil.isNull(bufferTimeAfter) ? 0 : Integer.parseInt(bufferTimeAfter));
                resource.setBackBufferDur(durationBufferTimeAfter);

                //制造时间取值方式
                String processDurationLogic = resourceData.getProcessDurationLogic();
                if (StringUtils.isNotEmpty(processDurationLogic)) {
                    resource.setProcessDurationLogic(com.yhl.aps.api.obj.Resource.DurationLogic.valueOf(processDurationLogic));
                }
                //动态设置和清洗时间取值方式
                String dynamicSetupandCleanupDurationLogic = resourceData.getDynamicSetupandCleanupDurationLogic();
                if (StringUtils.isNotEmpty(dynamicSetupandCleanupDurationLogic)) {
                    resource.setDynamicSetupandCleanupDurationLogic(com.yhl.aps.api.obj.Resource.DurationLogic.valueOf(dynamicSetupandCleanupDurationLogic));
                }
                //设置和清洗时间取值方式
                String setupandCleanupDurationLogic = resourceData.getSetupandCleanupDurationLogic();
                if (StringUtils.isNotEmpty(setupandCleanupDurationLogic)) {
                    resource.setSetupandCleanupDurationLogic(com.yhl.aps.api.obj.Resource.DurationLogic.valueOf(setupandCleanupDurationLogic));
                }
                if (CollectionUtils.isNotEmpty(resourceData.getAvailableTimes())) {
                    List<TimeSlotWithEfficiency> resCalendarList = new ArrayList<>();
                    for (RzzResourceAvailableTime availableTime : resourceData.getAvailableTimes()) {
                        TimeSlotWithEfficiency slot = new TimeSlotWithEfficiency();
                        slot.setBeginTime(convertDateToLocalDateTime(availableTime.getTimeBegin()));
                        slot.setBeginTime(convertDateToLocalDateTime(availableTime.getTimeBegin()));
                        slot.setEndTime(convertDateToLocalDateTime(availableTime.getTimeEnd()));
                        resCalendarList.add(slot);
                    }
                    resource.setCalendar(resCalendarList);
                }

//                Gson gson = new Gson();
                for (Map<String, Map<String, Object>> specReq : resourceData.getSpecReqs()) {
                    //遍历sepcReq,生成spec
                    specReq.forEach((key, value) -> {
                        value.forEach((key1, value1) -> {
                            resource.getSpecContainer().addSpec((com.yhl.aps.api.obj.Spec) value1);
                        });
                    });
//                    resource.getSpecContainer().addSpec(SpecGenerator.genSpec(gson.toJsonTree(specReq)));
                }

                //资源占位数据
                List<Map<String, Schedule>> fixedSchedules = resourceData.getFixedSchedules();

                if (CollectionUtils.isNotEmpty(fixedSchedules)) {
                    List<com.yhl.aps.api.obj.Schedule> scheduleVec = new ArrayList<>();
                    for (Map<String, Schedule> fixedSchedule : fixedSchedules) {
                        Schedule processSchedule = fixedSchedule.get("processSchedule");
                        Schedule setupSchedule = fixedSchedule.get("setupSchedule");
                        Schedule cleanupSchedule = fixedSchedule.get("cleanupSchedule");
                        if (null != processSchedule) {
                            ProcessSchedule schedule = new ProcessSchedule();
                            Operation operation = Operation.create();
                            operation.setId(processSchedule.getOperationId());
                            schedule.setOpId(processSchedule.getOperationId());
                            List<TimeSlotWithEfficiency> block = new ArrayList<>();
                            for (OccupyTime occupyTime : processSchedule.getOccupyTime()) {
                                if (null != occupyTime.getTimeBegin() && null != occupyTime.getTimeEnd()) {
                                    TimeSlotWithEfficiency slot = new TimeSlotWithEfficiency();
                                    slot.setBeginTime(convertDateToLocalDateTime(DateUtils.stringToDate(occupyTime.getTimeBegin(), DateUtils.COMMON_DATE_STR1)));
                                    slot.setEndTime(convertDateToLocalDateTime(DateUtils.stringToDate(occupyTime.getTimeEnd(), DateUtils.COMMON_DATE_STR1)));
                                    block.add(slot);
                                }
                            }
                            schedule.setOperation(operation);
                            schedule.setTimeSlots(block);
                            scheduleVec.add(schedule);
                        }
                        if (null != setupSchedule) {
                            SetupSchedule schedule = new SetupSchedule();
                            Operation operation = Operation.create();
                            operation.setId(setupSchedule.getOperationId());
                            schedule.setOpId(setupSchedule.getOperationId());
                            List<TimeSlotWithEfficiency> block = new ArrayList<>();
                            for (OccupyTime occupyTime : setupSchedule.getOccupyTime()) {
                                if (null != occupyTime.getTimeBegin() && null != occupyTime.getTimeEnd()) {
                                    TimeSlotWithEfficiency slot = new TimeSlotWithEfficiency();
                                    slot.setBeginTime(convertDateToLocalDateTime(DateUtils.stringToDate(occupyTime.getTimeBegin(), DateUtils.COMMON_DATE_STR1)));
                                    slot.setEndTime(convertDateToLocalDateTime(DateUtils.stringToDate(occupyTime.getTimeEnd(), DateUtils.COMMON_DATE_STR1)));
                                    block.add(slot);
                                }
                            }
                            schedule.setOperation(operation);
                            schedule.setTimeSlots(block);
                            scheduleVec.add(schedule);
                        }
                        if (null != cleanupSchedule) {
                            CleanupSchedule schedule = new CleanupSchedule();
                            Operation operation = Operation.create();
                            operation.setId(cleanupSchedule.getOperationId());
                            schedule.setOpId(cleanupSchedule.getOperationId());
                            List<TimeSlotWithEfficiency> block = new ArrayList<>();
                            for (OccupyTime occupyTime : cleanupSchedule.getOccupyTime()) {
                                if (null != occupyTime.getTimeBegin() && null != occupyTime.getTimeEnd()) {
                                    TimeSlotWithEfficiency slot = new TimeSlotWithEfficiency();
                                    slot.setBeginTime(convertDateToLocalDateTime(DateUtils.stringToDate(occupyTime.getTimeBegin(), DateUtils.COMMON_DATE_STR1)));
                                    slot.setEndTime(convertDateToLocalDateTime(DateUtils.stringToDate(occupyTime.getTimeEnd(), DateUtils.COMMON_DATE_STR1)));
                                    block.add(slot);
                                }
                            }
                            schedule.setOperation(operation);
                            schedule.setTimeSlots(block);
                            scheduleVec.add(schedule);
                        }
                    }
                    resource.setFixedSchedules(scheduleVec);
                }
                vec.add(resource);
            }
        }

        // 查询S1YZ主资源下所有物理资源
        List<CollectionValueVO> mainYztResource = ipsFeign.getByCollectionCode("S1YZ");
        if (CollectionUtils.isNotEmpty(mainYztResource)) {
            String collectionValue = mainYztResource.get(0).getCollectionValue();
            List<String> mainYztResourceId = operationTaskExtDao.selectMainYztResource(collectionValue);
            if (CollectionUtils.isNotEmpty(mainYztResourceId)) {
                baseAlgorithmData.setSplitResourceIds(mainYztResourceId);
            }
        }
        return vec;
    }

    private List<Operation> getOperationVec(RzzBaseAlgorithmData baseAlgorithmData, APSInput apsInput) {
        List<Operation> operationVec = new ArrayList<>();
        Map<String, com.yhl.aps.api.obj.Resource> resourceMap = new HashMap<>();
        List<com.yhl.aps.api.obj.Resource> resourceVec = apsInput.getResources();

        resourceVec.forEach(
                resource -> resourceMap.put(resource.getId(), resource)
        );
        Map<String, OperationWrapper> childOperationMapOfId = new HashMap<>();
        List<RzzOperationData> subOperations = baseAlgorithmData.getOperations().stream().filter(rzzOperationData -> StringUtils.isNotBlank(rzzOperationData.getParentId())).collect(Collectors.toList());
        Map<String, List<RzzOperationData>> operationGroupByParentId = subOperations.stream().collect(Collectors.groupingBy(RzzOperationData::getParentId));
        List<RzzOperationData> parentOperations = baseAlgorithmData.getOperations().stream().filter(operationData -> StringUtils.isBlank(operationData.getParentId())).collect(Collectors.toList());
        for (RzzOperationData operationData : parentOperations) {
            Operation operation = getOperation(operationData, resourceMap, childOperationMapOfId, baseAlgorithmData);
            if (CollectionUtils.isNotEmpty(operationGroupByParentId.get(operationData.getOperationId()))) {
                for (RzzOperationData subOperationData : operationGroupByParentId.get(operation.getId())) {
                    Operation subOperation = getOperation(subOperationData, resourceMap, childOperationMapOfId, baseAlgorithmData);
                    operation.addSplitOperation(subOperation);
                    if (subOperationData.getFeedBackQuantity().compareTo(BigDecimal.ZERO) > 0) {
                        JsonSpec spec = new JsonSpec();
                        spec.setKey("COMPLETE_QUANTITY:" + subOperation.getId());
                        spec.setName("COMPLETE_QUANTITY:" + subOperation.getId());
                        spec.setValue(new JsonPrimitive(subOperationData.getFeedBackQuantity().doubleValue()));
                        operation.getSpecContainer().getSpecs().add(spec);
                    }
                }
            }
            operationVec.add(operation);
        }
        return operationVec;
    }

    private Operation getOperation(RzzOperationData operationData, Map<String, com.yhl.aps.api.obj.Resource> resourceMap, Map<String, OperationWrapper> childOperationMapOfId, RzzBaseAlgorithmData baseAlgorithmData) {
        OperationWrapper operation = (OperationWrapper) Operation.create();
        String operationStatus = operationData.getStatus();
        if (StringUtils.isNotBlank(operationStatus)) {
            if (operationStatus.equals("locked")) {
                operation.setStatus(Operation.Status.Locked);
            } else if (operationStatus.equals("complete")) {
                operation.setStatus(Operation.Status.Complete);
            }
        }
        List<Spec> specs = new ArrayList<>();
        if (StrUtil.isNotEmpty(operationData.getWorkWearCategory())) {
            JsonSpec spec = new JsonSpec();
            spec.setKey("GZMJ");
            spec.setName("GZMJ");
            spec.setValue(new JsonPrimitive(operationData.getWorkWearCategory()));
            specs.add(spec);
        }
        if (StrUtil.isNotBlank(operationData.getProductType())) {
            JsonSpec spec = new JsonSpec();
            spec.setKey("CPLX");
            spec.setName("CPLX");
            spec.setValue(new JsonPrimitive(operationData.getProductType()));
            specs.add(spec);
        }
        operation.setSpecs(specs);
        if (childOperationMapOfId.containsKey(operationData.getOperationId())) {
            operation = childOperationMapOfId.get(operationData.getOperationId());
        }
        if (operationData.getLastScheduled() != null) {
            Operation.LastScheduleInfo lastScheduleInfo = new Operation.LastScheduleInfo();
            Set<String> resourceSet = new HashSet<>();
            resourceSet.addAll(operationData.getLastScheduled().getResourceIds());
            lastScheduleInfo.setResources(resourceSet);
            lastScheduleInfo.setProcessStart(LocalDateTime.ofInstant(operationData.getLastScheduled().getProcessStart().toInstant(), ZoneId.systemDefault()));
            lastScheduleInfo.setProcessEnd(LocalDateTime.ofInstant(operationData.getLastScheduled().getProcessEnd().toInstant(), ZoneId.systemDefault()));
            operation.setLastScheduledInfo(lastScheduleInfo);
        }

//        operation.setCleanupDur(operationData.getCleanupDuration());
        operation.setId(operationData.getOperationId());
        operation.setQuantity(operationData.getQuantity().doubleValue());
        List<CandidateResource> candidateResources = new ArrayList<>();
        for (OperationOnResource operationOnResource : operationData.getOperationOnResource()) {
            CandidateResource candidateResource = new CandidateResource();
            candidateResource.setMatchCode(operationOnResource.getMatchCode());
            candidateResource.setAltToolCode(operationOnResource.getAltToolCode());
            com.yhl.aps.api.obj.Resource resource = resourceMap.get(operationOnResource.getResourceId());
            candidateResource.setId(operationOnResource.getOperationOnResourceId());
            if (null != resource) {
                candidateResource.setSolution(resource.getCurrentSolution());
                candidateResource.setResourceId(resource.getId());
            }
            // 制造
            DurationCalInfo durProcess = new DurationCalInfo();
            if (null != operationOnResource.getProcessDuration() && operationOnResource.getProcessDuration().getFixedDurSeconds() > 0) {
                durProcess.setFixedDur(Duration.ofSeconds(operationOnResource.getProcessDuration().getFixedDurSeconds()));
                durProcess.setUnitBatchSize(operationOnResource.getProductionUnitBatchSize().intValue());
                candidateResource.setProcessRate(durProcess);
            } else if (null != operationOnResource.getProcessDuration() && operationOnResource.getProcessDuration().getTaskDurationEffective() != null) {
                DurationCalInfo.Effective effective = new DurationCalInfo.Effective();
                effective.setDur(Duration.ofSeconds(operationOnResource.getProcessDuration().getTaskDurationEffective().getDur()));
                effective.setCount(operationOnResource.getProcessDuration().getTaskDurationEffective().getQuantity().doubleValue());
                durProcess.setEffective(effective);
                durProcess.setUnitBatchSize(operationOnResource.getProductionUnitBatchSize().intValue());
                candidateResource.setProcessRate(durProcess);
            }
            // 清洗
            DurationCalInfo durCleanup = new DurationCalInfo();
            if (null != operationOnResource.getCleanupDuration() && operationOnResource.getCleanupDuration().getFixedDurSeconds() > 0) {
                durCleanup.setFixedDur(Duration.ofSeconds(operationOnResource.getCleanupDuration().getFixedDurSeconds()));
                durCleanup.setUnitBatchSize(operationOnResource.getCleanupUnitBatchSize().intValue());
                candidateResource.setCleanupRate(durCleanup);
            } else if (null != operationOnResource.getCleanupDuration() && operationOnResource.getCleanupDuration().getTaskDurationEffective() != null) {
                DurationCalInfo.Effective effective = new DurationCalInfo.Effective();
                effective.setDur(Duration.ofSeconds(operationOnResource.getCleanupDuration().getTaskDurationEffective().getDur()));
                effective.setCount(operationOnResource.getCleanupDuration().getTaskDurationEffective().getQuantity().doubleValue());
                durCleanup.setEffective(effective);
                durCleanup.setUnitBatchSize(operationOnResource.getCleanupUnitBatchSize().intValue());
                candidateResource.setCleanupRate(durCleanup);
            }
            // 设置
            DurationCalInfo durSetup = new DurationCalInfo();
            if (null != operationOnResource.getSetupDuration() && operationOnResource.getSetupDuration().getFixedDurSeconds() > 0) {
                durSetup.setFixedDur(Duration.ofSeconds(operationOnResource.getSetupDuration().getFixedDurSeconds()));
                durSetup.setUnitBatchSize(operationOnResource.getSetupUnitBatchSize().intValue());
                candidateResource.setSetupRate(durSetup);
            } else if (null != operationOnResource.getSetupDuration() && operationOnResource.getSetupDuration().getTaskDurationEffective() != null) {
                DurationCalInfo.Effective effective = new DurationCalInfo.Effective();
                effective.setDur(Duration.ofSeconds(operationOnResource.getSetupDuration().getTaskDurationEffective().getDur()));
                effective.setCount(operationOnResource.getSetupDuration().getTaskDurationEffective().getQuantity().doubleValue());
                durSetup.setEffective(effective);
                durSetup.setUnitBatchSize(operationOnResource.getSetupUnitBatchSize().intValue());
                candidateResource.setSetupRate(durSetup);
            }
            if (null == operationOnResource.getPriority()) {
                candidateResource.setPriority(1);

            } else {
                candidateResource.setPriority(operationOnResource.getPriority());
            }
            candidateResources.add(candidateResource);
        }
        // 根据优先级，资源ID排序后，重新设置资源优先级
        AtomicInteger initPriority = new AtomicInteger(0);
        candidateResources.stream().sorted(
                        Comparator.comparing(CandidateResource::getPriority)
                                .thenComparing(CandidateResource::getResourceId))
                .forEach(x -> x.setPriority(initPriority.incrementAndGet()));
        if (operationData.getEarliestBeginTime() != null) {
            operation.setEarliestStart(LocalDateTime.ofInstant(operationData.getEarliestBeginTime().toInstant(), ZoneId.systemDefault()));
        }
        if (null != operationData.getLatestEndTime()) {
            operation.setLatestEnd(LocalDateTime.ofInstant(operationData.getLatestEndTime().toInstant(), ZoneId.systemDefault()));
        }

        List<InputMaterial> operationInputMaterial = operationData.getOperationInputMaterial();
        List<InputProduct> inputProducts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(operationInputMaterial)) {
            operationInputMaterial.forEach(inputMaterial -> {
                InputProduct inputProduct = new InputProduct();
                inputProduct.setProductId(inputMaterial.getProductId());
                inputProduct.setStockingPointId(inputMaterial.getStockingPointId());
                inputProduct.setMainPart(YesOrNoEnum.YES.getCode().equals(inputMaterial.getMainPart()));
                inputProducts.add(inputProduct);
            });
        }
        operation.setInputItems(inputProducts);


        List<OutputMaterial> operationOutputMaterial = operationData.getOperationOutputMaterial();
        List<OutputProduct> outputProducts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(operationOutputMaterial)) {
            operationOutputMaterial.forEach(outputMaterial -> {
                OutputProduct outputProduct = new OutputProduct();
                outputProduct.setProductId(outputMaterial.getProductId());
                outputProduct.setStockingPointId(outputMaterial.getStockingPointId());
                outputProduct.setMainProduct(YesOrNoEnum.YES.equals(outputMaterial.getMainProduct()));
                outputProducts.add(outputProduct);
            });
        }
        operation.setOutputItems(outputProducts);

        if (CollectionUtils.isNotEmpty(operationData.getTags())) {
            operation.setTags(operationData.getTags());
        }
        //设置镀膜和成型工序之间的最大最小间隔时间
        ConnInfo connInfo = new ConnInfo();
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_11.getCode())) {
            String max = operationData.getMaxTimeConstraint() == null ? "0" : operationData.getMaxTimeConstraint();
            connInfo.setMaxTimeConstrWithPrev(Duration.ofHours(Long.parseLong(max)));
        }
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_12.getCode())) {
            String min = operationData.getMinTimeConstraint() == null ? "0" : operationData.getMinTimeConstraint();
            connInfo.setMinTimeConstrWithPrev(Duration.ofHours(Long.parseLong(min)));
        }
        if (StringUtils.isNotEmpty(operationData.getConnType())){
            connInfo.setConnType(ConnType.valueOf(operationData.getConnType()));
        }
        operation.setConnInfo(connInfo);


        operation.setPriority(1);
        operation.setCandidateResources(candidateResources);
        return operation;
    }

    private List<com.yhl.aps.api.obj.Resource> updateResourceVec(APSInput apsInput) {
        List<com.yhl.aps.api.obj.Resource> resources = apsInput.getResources();
        //父工序
        List<Operation> parentOperations = apsInput.getOperations();
        //子工序
        List<Operation> childOperations = new ArrayList<>();
        parentOperations.forEach(operation -> childOperations.addAll(operation.getSplittedOperations()));
        for (com.yhl.aps.api.obj.Resource resource : resources) {
            List<com.yhl.aps.api.obj.Schedule> fixedSchedules = resource.getFixedSchedules();
            for (com.yhl.aps.api.obj.Schedule fixedSchedule : fixedSchedules) {
                String operationId = fixedSchedule.getOperationId();
                if (!ObjUtil.isNull(operationId)) {
                    List<Operation> operations = childOperations.stream().filter(o -> o.getId().equals(operationId)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(operations)) {
                        Operation newOperation = operations.get(0);
                        fixedSchedule.setOperation(newOperation);
                    }
                }
            }
        }
        return resources;
    }

    private List<WorkOrder> getWorkOrderVec(RzzBaseAlgorithmData baseAlgorithmData, APSInput apsInput) {
        List<WorkOrder> workOrderVec = new ArrayList<>();
        List<RzzWorkOrderData> workOrders = baseAlgorithmData.getWorkOrders();
        if (CollectionUtils.isEmpty(workOrders)) {
            return workOrderVec;
        }
        Map<String, Operation> operationMap = apsInput.getOperations().stream().collect(Collectors.toMap(Operation::getId, Function.identity()));
        Map<String, Product> productMap = apsInput.getProducts().stream().collect(Collectors.toMap(Product::getId, Function.identity(), (k1, k2) -> k2));
        Map<String, List<RzzOperationData>> operationDataGroupByWorkOrderId = baseAlgorithmData.getOperations().stream().filter(t -> StringUtils.isBlank(t.getParentId())).collect(Collectors.groupingBy(RzzOperationData::getWorkOrderId));

        for (RzzWorkOrderData workOrderData : workOrders) {
            WorkOrder workOrder = new WorkOrder();

            workOrder.setId(workOrderData.getId());
            if (workOrderData.getEarliestBeginTime() != null) {
                workOrder.setEarliestBeginTime(LocalDateTime.ofInstant(workOrderData.getEarliestBeginTime().toInstant(), ZoneId.systemDefault()));
            }
            if (workOrderData.getDueDate() != null) {
                workOrder.setDueDateTime(LocalDateTime.ofInstant(workOrderData.getDueDate().toInstant(), ZoneId.systemDefault()));
            }
            workOrder.setQuantity(workOrderData.getQuantity().doubleValue());
            if (ObjUtil.isNull(workOrderData.getPriority())) {
                //初始化0，历史制造订单无优先级，优先级最高
                workOrder.setPriority(0);
            } else {
                workOrder.setPriority(workOrderData.getPriority());
            }
            List<RzzOperationData> operationDataList = operationDataGroupByWorkOrderId.get(workOrder.getId());
            if (CollectionUtils.isEmpty(operationDataList)) {
                continue;
            }
            for (RzzOperationData operationData : operationDataList) {
                Operation operation = operationMap.get(operationData.getOperationId());
                workOrder.addOperation(operation);
            }
            Product product = productMap.get(workOrderData.getProductId());
            workOrder.setProduct(product);

            //顶层制造订单
            List<RzzWorkOrderData> topWorkOrderList = workOrderData.getTopWorkOrderList();
            List<CustomerOrder> customerOrders = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(topWorkOrderList)) {
                for (RzzWorkOrderData orderData : topWorkOrderList) {
                    CustomerOrder customerOrder = new CustomerOrder();
                    customerOrder.setId(orderData.getId());
                    if (workOrderData.getEarliestBeginTime() != null) {
                        workOrder.setEarliestBeginTime(LocalDateTime.ofInstant(workOrderData.getEarliestBeginTime().toInstant(), ZoneId.systemDefault()));
                    }
                    if (orderData.getDueDate() != null) {
                        customerOrder.setDueDateTime(LocalDateTime.ofInstant(orderData.getDueDate().toInstant(), ZoneId.systemDefault()));
                    }
                    customerOrder.setQuantity(orderData.getQuantity().doubleValue());
                    if (ObjUtil.isNull(orderData.getPriority())) {
                        //初始化最大值
                        customerOrder.setPriority(Integer.MAX_VALUE);
                    } else {
                        customerOrder.setPriority(orderData.getPriority());
                    }
                    customerOrders.add(customerOrder);
                }
            }
            workOrder.setCustomerOrders(customerOrders);

            workOrderVec.add(workOrder);
        }
        return workOrderVec;
    }

    protected abstract void writeConf(APSInput apsInput, RzzBaseAlgorithmData baseAlgorithmData);

    protected JSONObject getConf() {
        JSONObject config = new JSONObject();
        config.put("modules", new JSONArray());
        return config;
    }

    protected JSONObject getConfLoader(Date beginTime, Date endTime) {
        JSONObject jsonObject = new JSONObject();
        JSONObject confLoader = new JSONObject();
        confLoader.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
        confLoader.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
        confLoader.put("dumpDebugDetail", true);
        confLoader.put("outputFolder", "output");
        jsonObject.put("confLoader", confLoader);
        return jsonObject;
    }

    protected JSONObject getRuleLoader() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ruleLoader", new JSONObject());
        return jsonObject;
    }

    protected JSONObject getContinuousProductionModule() {
        JSONObject jsonObject = new JSONObject();
        JSONObject operationTag = new JSONObject();
        operationTag.put("operationTag", "连续生产");
        jsonObject.put("continuousProductionModule", operationTag);
        return jsonObject;
    }

    protected JSONObject getInsertOrderModule() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("insertOrderModule", new JSONObject());
        return jsonObject;
    }

    protected JSONArray getCapacityList(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONArray capacityList = new JSONArray();
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_3.getCode())) {
            //后工序库容限制
            JSONObject storageLimitForQuantity = new JSONObject();
            storageLimitForQuantity.put("storageLimitForQuantity", getStorageLimitForQuantity(baseAlgorithmData));
            capacityList.add(storageLimitForQuantity);
        }
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.INTERLAYER_RULE_3.getCode())) {
            //连续炉开机规则
            JSONObject BoundaryStrategies = new JSONObject();
            BoundaryStrategies.put("BoundaryStrategies", new JSONObject());
            capacityList.add(BoundaryStrategies);
        }
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_4.getCode())) {
            //特殊工艺产能约束
            List<RzzSpecialLimitData> specialLimitDataList = baseAlgorithmData.getSpecialLimitDataList();
            for (RzzSpecialLimitData specialLimitData : specialLimitDataList) {
                JSONObject specCounterForQuantity = getSpecCounterForQuantity(specialLimitData);
                JSONObject specCounterForQuantityJson = new JSONObject();
                specCounterForQuantityJson.put("specCounterForQuantity", specCounterForQuantity);
                capacityList.add(specCounterForQuantityJson);
            }
        }

        return capacityList;
    }

    protected JSONObject getStorageLimitForQuantity(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject effectiveTime = new JSONObject();
        effectiveTime.put("timeBegin", DateUtils.dateToString(baseAlgorithmData.getScheduleParamDetail().getBeginTime(), DateUtils.COMMON_DATE_STR1));
        effectiveTime.put("timeEnd", DateUtils.dateToString(baseAlgorithmData.getScheduleParamDetail().getEndTime(), DateUtils.COMMON_DATE_STR1));
        JSONArray effectiveTimeList = new JSONArray();
        effectiveTimeList.add(effectiveTime);
        JSONArray limitInfoList = getLimitInfo(baseAlgorithmData);
        JSONObject storageLimitForQuantity = new JSONObject();
        storageLimitForQuantity.put("effectiveTime", effectiveTimeList);
        storageLimitForQuantity.put("limitInfo", limitInfoList);
        return storageLimitForQuantity;
    }

    protected JSONObject getSpecCounterForQuantity(RzzSpecialLimitData specialLimitData) {
        JSONArray specs = new JSONArray();
        specs.add(specialLimitData.getSpec());
        JSONArray effectiveTimeList = new JSONArray();
        for (Pair<String, String> pair : specialLimitData.getEffectiveTimeList()) {
            JSONObject effectiveTime = new JSONObject();
            effectiveTime.put("timeBegin", pair.getLeft());
            effectiveTime.put("timeEnd", pair.getRight());
            effectiveTimeList.add(effectiveTime);
        }
        JSONObject specCounterForQuantity = new JSONObject();
        specCounterForQuantity.put("specs", specs);
        specCounterForQuantity.put("effectiveTime", effectiveTimeList);
        specCounterForQuantity.put("maxQuantity", specialLimitData.getMaxQuantity());
        return specCounterForQuantity;
    }

    protected JSONObject getResourceMaintenanceList(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONArray maintenanceInfos = new JSONArray();
        for (Map.Entry<String, Pair<BigDecimal, String>> entry : baseAlgorithmData.getEquipmentMaintenanceQuantityMap().entrySet()) {

            JSONObject continuousProductionForQuantity = new JSONObject();
            continuousProductionForQuantity.put("resourceId", entry.getKey());
            continuousProductionForQuantity.put("quantity", entry.getValue().getLeft());
            //todo 目前默认2小时
            continuousProductionForQuantity.put("maintenanceDur", entry.getValue().getRight());
            JSONObject quantity = new JSONObject();
            quantity.put("continuousProductionForQuantity", continuousProductionForQuantity);
            maintenanceInfos.add(quantity);
        }
        JSONObject maintenanceInfo = new JSONObject();
        maintenanceInfo.put("maintenanceInfos", maintenanceInfos);
        return maintenanceInfo;
    }

    protected JSONArray getLimitInfo(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONArray limitInfoList = new JSONArray();
        /*for (Map.Entry<String, ProductionCapacityVO> entry : baseAlgorithmData.getWithLoadingPositionOfCapcityMap().entrySet()) {
            JSONObject limitInfo = new JSONObject();
            limitInfo.put("operationTag", entry.getKey());
            limitInfo.put("maxStorageQuantity", entry.getValue().getProcedureCapacityActual());
            limitInfoList.add(limitInfo);
        }
        for (Map.Entry<String, ProductionCapacityVO> entry : baseAlgorithmData.getWithNotLoadingPositionOfCapcityMap().entrySet()) {
            JSONObject limitInfo = new JSONObject();
            limitInfo.put("operationTag", entry.getKey());
            limitInfo.put("maxStorageQuantity", entry.getValue().getProcedureCapacityActual());
            limitInfoList.add(limitInfo);
        }*/
        Map<String, LoadingPositionCapacityVO> loadingPositionOfCapcityMap = baseAlgorithmData.getLoadingPositionOfCapcityMap();
        for (Map.Entry<String, LoadingPositionCapacityVO> entry : loadingPositionOfCapcityMap.entrySet()) {
            JSONObject limitInfo = new JSONObject();
            limitInfo.put("operationTag", entry.getKey());
            limitInfo.put("maxStorageQuantity", entry.getValue().getMaxStorageQuantity());
            limitInfo.put("currentQauantity", entry.getValue().getCurrentQauantity());
            limitInfoList.add(limitInfo);
        }
        return limitInfoList;
    }

    protected JSONObject getResultDumper() {
        JSONObject result = new JSONObject();
        result.put("resultFile", "result_file.json");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("resultDumper", result);
        return jsonObject;
    }

    protected JSONObject getAutoAdjustThatOperationsSatisfyDeliveryByLatestEndConf(List<Map<String, Map<String, String>>> invertResourceSelectionRule, Boolean currentResourceInverted) {
        JSONObject jsonObject = new JSONObject();
        JSONObject invertResourceSelectionRuleJsonObject = new JSONObject();
        if (!currentResourceInverted) {
            JSONObject sortStrategyForResource = new JSONObject();
            sortStrategyForResource.put("strategies", invertResourceSelectionRule);
            invertResourceSelectionRuleJsonObject.put("sortStrategyForResource", sortStrategyForResource);
        }
        jsonObject.put("autoAdjustThatOperationsSatisfyDeliveryByLatestEnd", invertResourceSelectionRuleJsonObject);
        return jsonObject;
    }

    protected JSONObject getSplitInsertOrderModule(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject jsonObject = new JSONObject();
        JSONArray operationTags = new JSONArray();
//        operationTags.add("coating");
        operationTags.add("forming");
        jsonObject.put("operationTags", operationTags);
        jsonObject.put("outChangeOvers", baseAlgorithmData.getMoldChangeTimeDataList());
        jsonObject.put("splitResourceIds", baseAlgorithmData.getSplitResourceIds());
        JSONObject splitInsertOrderModule = new JSONObject();
        splitInsertOrderModule.put("splitInsertOrderModule", jsonObject);
        return splitInsertOrderModule;
    }

    /**
     * 规则自动排程命令参数
     *
     * @return
     */
    public ScheduleParamDetail getScheduleParamDetail(PlanningHorizonVO schedulePeriod) {

        List<GlobalConfigsVO> allGlobalConfigsVOs = mdsFeign.getAllGlobalConfigsVOs();
        Map<String, Object> params = new HashMap<>();
        params.put("calculateModuleId", "8f7f0bcc-018f-7f1af06e-ff808081-0007");
        ProductionSchedulingParamVO paramScheduling = mdsFeign.getProductionSchedulingByParams(params).get(0);

        ScheduleParamDetail scheduleParamDetail = new ScheduleParamDetail();
        scheduleParamDetail.setBeginTime(schedulePeriod.getPlanStartTime());
        scheduleParamDetail.setEndTime(schedulePeriod.getPlanEndTime());
        JSONArray operatorSortStrategyArray = new JSONArray();
        if (paramScheduling != null) {
            scheduleParamDetail.setInWorkOrder(YesOrNoEnum.YES.getCode().equals(paramScheduling.getSortByOrder()));
            scheduleParamDetail.setScheduleDirection(paramScheduling.getSchedulingDirection());
            scheduleParamDetail.setOperationAssignStrategy(paramScheduling.getResourceJoinRule());
            scheduleParamDetail.setSchedulingMethod(paramScheduling.getSchedulingMethod());
            scheduleParamDetail.setDelayAnalysis(paramScheduling.getDelayAnalysis());
            scheduleParamDetail.setCurrentResourceInverted(org.apache.commons.lang3.StringUtils.equals(YesOrNoEnum.YES.getCode(), paramScheduling.getCurrentResourceInverted()));
            scheduleParamDetail.setFilterConditions(paramScheduling.getFilterConditions());
            for (GlobalConfigsVO globalConfigsVO : allGlobalConfigsVOs) {
                if (globalConfigsVO.getParamCode().equals("same_product_no_switch")) {
                    scheduleParamDetail.setNoSwitchIfSameProduct(YesOrNoEnum.YES.getCode().equals(globalConfigsVO.getParamValue()));
                }
            }

            // 2025-03-09注释，由自定义排序模块实现
//            String sortRules = paramScheduling.getSortRule();
//            JSONArray sortRuleArray = JSONArray.parseArray(sortRules);
//            for (int i = 0; i < sortRuleArray.size(); i++) {
//                JSONObject jsonObject = sortRuleArray.getJSONObject(i);
//                JSONObject operatorSortStrategy = new JSONObject();
//                JSONObject jsonObjectInner = new JSONObject();
//                jsonObjectInner.put("rule", jsonObject.getString("sort"));
//                operatorSortStrategy.put(jsonObject.getString("rule"), jsonObjectInner);
//                operatorSortStrategyArray.add(operatorSortStrategy);
//            }

            //资源选择策略
            JSONArray jsonArray = JSONArray.parseArray(paramScheduling.getResourceChooseRule());
            List<Map<String, Map<String, String>>> resourceSelectionStrategy = new ArrayList<Map<String, Map<String, String>>>();
            for (Object object : jsonArray) {
                JSONObject jsonObject = (JSONObject) object;
                Map<String, Map<String, String>> outMap = new HashMap<String, Map<String, String>>();
                Map<String, String> innerMap = new HashMap<String, String>();
                //首字母改成小写
                innerMap.put("rule", jsonObject.getString("rule"));

                String toleranceDurDay = jsonObject.getString("toleranceDurDay");//天
                BigDecimal toleranceDurDayBigDecimal = null;
                if (ObjectUtils.isNotEmpty(toleranceDurDay)) {
                    toleranceDurDayBigDecimal = new BigDecimal(toleranceDurDay);
                } else {
                    toleranceDurDayBigDecimal = new BigDecimal(0);
                }
                String toleranceDurHour = jsonObject.getString("toleranceDurHour");//小时
                BigDecimal toleranceDurHourBigDecimal = null;
                if (ObjectUtils.isNotEmpty(toleranceDurHour)) {
                    toleranceDurHourBigDecimal = new BigDecimal(toleranceDurHour);
                } else {
                    toleranceDurHourBigDecimal = new BigDecimal(0);
                }
                String toleranceDurMinute = jsonObject.getString("toleranceDurMinute");//分钟
                BigDecimal toleranceDurMinuteBigDecimal = null;
                if (ObjectUtils.isNotEmpty(toleranceDurMinute)) {
                    toleranceDurMinuteBigDecimal = new BigDecimal(toleranceDurMinute);
                } else {
                    toleranceDurMinuteBigDecimal = new BigDecimal(0);
                }
                //容忍天数转成小时
                BigDecimal toleranceDurBigDecimal = toleranceDurDayBigDecimal.multiply(new BigDecimal(24)).add(toleranceDurHourBigDecimal);

                String toleranceDurString = String.format("%02d", toleranceDurBigDecimal.intValue()) + ":" + String.format("%02d", toleranceDurMinuteBigDecimal.intValue()) + ":00";
                innerMap.put("toleranceDur", toleranceDurString);
                //innerMap.put("toleranceDur", jsonObject.getString("toleranceDur"));
                String ruleName = jsonObject.getString("ruleName");
                outMap.put(lowercaseFirstLetter(ruleName), innerMap);
                if (org.apache.commons.lang3.StringUtils.isBlank(ruleName)) {
                    continue;
                }
                resourceSelectionStrategy.add(outMap);
            }
            scheduleParamDetail.setResourceSelectionStrategy(resourceSelectionStrategy);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(paramScheduling.getInvertResourceSelectionRule()) && org.apache.commons.lang3.StringUtils.equals(paramScheduling.getInventoryOptimization(), YesOrNoEnum.YES.getCode())) {
                JSONArray invertResourceSelectionRuleArray = JSONArray.parseArray(paramScheduling.getInvertResourceSelectionRule());
                List<Map<String, Map<String, String>>> invertResourceSelectionRuleStrategy = new ArrayList<>();
                for (Object object : invertResourceSelectionRuleArray) {
                    JSONObject jsonObject = (JSONObject) object;
                    Map<String, Map<String, String>> outMap = new HashMap<>();
                    Map<String, String> innerMap = new HashMap<>();
                    //首字母改成小写
                    innerMap.put("rule", jsonObject.getString("rule"));
                    innerMap.put("toleranceDur", jsonObject.getString("toleranceDur"));
                    String ruleName = jsonObject.getString("ruleName");
                    outMap.put(lowercaseFirstLetter(ruleName), innerMap);
                    invertResourceSelectionRuleStrategy.add(outMap);
                }
                scheduleParamDetail.setInvertResourceSelectionRule(invertResourceSelectionRuleStrategy);
            }
        }

//        operatorSortStrategyArray.add(getJsonObj("GZMJ"));
//        operatorSortStrategyArray.add(getJsonObj("CPLX"));
//        operatorSortStrategyArray.add(getJsonObj("CUSTOM_ORDER"));
        // 增加制造订单优先级排序
        operatorSortStrategyArray.add(getOperationCompareByWorkOrderPriority());
        // 增加订单交期排序
        operatorSortStrategyArray.add(getOperationCompareByWorkOrderDueDate());
        scheduleParamDetail.setOperatorSortStrategy(operatorSortStrategyArray);

        scheduleParamDetail.setWhetherInsertOrderMiddleModule(false);
        return scheduleParamDetail;
    }

    private JSONObject getJsonObj(String obj) {
        JSONObject valueForSpecObj = new JSONObject();
        JSONObject valueForSpec = new JSONObject();
        JSONObject jsonObjectInner2 = new JSONObject();
        jsonObjectInner2.put("key", obj);
        jsonObjectInner2.put("name", obj);
        valueForSpec.put("spec", jsonObjectInner2);
        valueForSpec.put("rule", "ASC");
        valueForSpecObj.put("rzzValueForSpec", valueForSpec);
        return valueForSpecObj;
    }

    private JSONObject getOperationCompareByWorkOrderPriority() {
        JSONObject value = new JSONObject();
        JSONObject valueForWo = new JSONObject();
        valueForWo.put("rule", "ASC");
        value.put("priorityForWorkOrder", valueForWo);
        return value;
    }

    private JSONObject getOperationCompareByWorkOrderDueDate() {
        JSONObject value = new JSONObject();
        JSONObject valueForWo = new JSONObject();
        valueForWo.put("rule", "ASC");
        value.put("dueDateForWorkOrder", valueForWo);
        return value;
    }

    private String lowercaseFirstLetter(String str) {
        if (str == null || str.length() == 0) {
            return str;
        }
        char firstChar = Character.toLowerCase(str.charAt(0));
        String remainingStr = str.substring(1);
        return firstChar + remainingStr;
    }

    protected JSONArray getSwitchDurForProductList(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONArray coatingChangeTimeJsonList = new JSONArray();

        List<RzzProductInSockingPointData> productInSockingPoints = baseAlgorithmData.getProductInSockingPoints();
        Map<String, RzzProductInSockingPointData> productInSockingPointDataMap = productInSockingPoints.stream()
                .collect(Collectors.toMap(p -> StrUtil.join("&", p.getStockPointCode(), p.getProductCode()), Function.identity()));

        List<StandardStepVO> standardStepVOS = baseAlgorithmData.getStandardStepVOS();
        Map<String, String> standardStepTypeMap = standardStepVOS.stream()
                .collect(Collectors.toMap(
                        p -> StrUtil.join("&", p.getStockPointCode(), p.getStandardStepCode()),
                        StandardStepVO::getStandardStepType
                ));
        List<MoldChangeTimeVO> moldChangeTimeVOS = baseAlgorithmData.getMoldChangeTimeVOS();
        Map<String, PhysicalResourceVO> physicalResourceVOMap = newMdsFeign.selectAllPhysicalResource(null).stream()
                .collect(Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, Function.identity()));
        String formingProcess = StandardStepEnum.FORMING_PROCESS.getCode();
        List<String> productCodes = StreamUtils.columnToList(moldChangeTimeVOS, MoldChangeTimeVO::getProductCode);
        Map<String, Object> params = new HashMap<>();
        params.put("productCodeList", productCodes);
        List<MdsProductStockPointBaseVO> stockPointBaseVOS = newMdsFeign.selectProductStockPointBaseByParams(SystemHolder.getScenario(), params);
        Map<String, String> productBaseMap = stockPointBaseVOS.stream()
                .filter(p -> StrUtil.isNotBlank(p.getStandardResourceId()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getStandardResourceId, (existing, replacement) -> existing));
        for (MoldChangeTimeVO moldChangeTimeVO : moldChangeTimeVOS) {
            String stockPointCode = moldChangeTimeVO.getStockPointCode();
            String productCode = moldChangeTimeVO.getProductCode();
            String operationCode = moldChangeTimeVO.getOperationCode();
            String resourceCode = moldChangeTimeVO.getResourceCode();
            String key = StrUtil.join("&", stockPointCode, operationCode);
            String key2 = StrUtil.join("&", stockPointCode, productCode);
            String type = standardStepTypeMap.get(key);
            if (!formingProcess.equals(type)) {
                continue;
            }
            if (!productInSockingPointDataMap.containsKey(key2)) {
                continue;
            }
            if (!physicalResourceVOMap.containsKey(resourceCode)) {
                continue;
            }
            PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(resourceCode);
            String physicalResourceId = physicalResourceVO.getId();
            NewProductStockPointVO productStockPointVO = productInSockingPointDataMap.get(key2).getProductStockPointVO();

            Long dieChangeTime = moldChangeTimeVO.getDieChangeTime();
            JSONObject detailJson = new JSONObject();
            detailJson.put("resourceGroupId", physicalResourceVO.getStandardResourceId());
            detailJson.put("resourceId", physicalResourceId);
            detailJson.put("priority", 1);
            //前序物品
            JSONObject prevJson = new JSONObject();
            prevJson.put("series", "!");
            prevJson.put("productId", "*");
            prevJson.put("dur", DateUtils.second2HourMinuteSecondStr(baseAlgorithmData.getYztSwitchSeconds()));
            detailJson.put("prev", prevJson);
            //后序物品：对应真实物品
            JSONObject nextJson = new JSONObject();
            // 物品车型代码
//            String series = productBaseMap.getOrDefault(productCode, productStockPointVO.getVehicleModelCode());
//            nextJson.put("series", series);
            nextJson.put("series", StrUtil.isEmpty(productStockPointVO.getVehicleModelCode()) ? "*" : productStockPointVO.getVehicleModelCode());
            // 物品id
            nextJson.put("productId", productStockPointVO.getId());
            nextJson.put("dur", DateUtils.second2HourMinuteSecondStr(dieChangeTime * 60));
            detailJson.put("next", nextJson);
            coatingChangeTimeJsonList.add(detailJson);
        }
        return coatingChangeTimeJsonList;
    }

    protected JSONArray getSwitchDurForSpecList(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONArray switchDurForSpecList = new JSONArray();
        Map<String, String> filmsCollectionValueMap = baseAlgorithmData.getFilmsCollectionValueMap();
        List<MpsCoatingChangeTimeVO> coatingChangeTimeList = baseAlgorithmData.getCoatingChangeTimeList();
        for (MpsCoatingChangeTimeVO mpsCoatingChangeTimeVO : coatingChangeTimeList) {
            JSONObject detailJson = new JSONObject();
            detailJson.put("resourceGroupId", "*");
            detailJson.put("resourceId", "*");
            detailJson.put("priority", 1);
            //前序物品
            String changeFilmBeforeCode = mpsCoatingChangeTimeVO.getChangeFilmBefore();
            String changeFilmBeforeName = filmsCollectionValueMap.get(changeFilmBeforeCode);
            JSONObject prevJson = new JSONObject();
            prevJson.put("key", changeFilmBeforeName);
            prevJson.put("name", changeFilmBeforeName);
            prevJson.put("value", changeFilmBeforeName);
            prevJson.put("dur", DateUtils.second2HourMinuteSecondStr(mpsCoatingChangeTimeVO.getCoatingProdChange()));
            detailJson.put("prev", prevJson);
            //后序物品
            JSONObject nextJson = new JSONObject();
            String changeFilmNextCode = mpsCoatingChangeTimeVO.getChangeFilmNext();
            String changeFilmNextName = filmsCollectionValueMap.get(changeFilmNextCode);
            nextJson.put("key", changeFilmNextName);
            nextJson.put("name", changeFilmNextName);
            nextJson.put("value", changeFilmNextName);
            nextJson.put("dur", "00:00:00");
            detailJson.put("next", nextJson);
            switchDurForSpecList.add(detailJson);
        }
        return switchDurForSpecList;
    }

    protected JSONArray getSwitchDurForToolList(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONArray switchDurForToolList = new JSONArray();
        Set<String> yztResourceIds = baseAlgorithmData.getYztResourceIds();
        if (CollectionUtils.isEmpty(yztResourceIds)) {
            return switchDurForToolList;
        }
        for (String resourceId : yztResourceIds) {
            JSONObject detailJson = new JSONObject();
            detailJson.put("resourceGroupId", "*");
            detailJson.put("resourceId", "*");
            detailJson.put("priority", 1);
            //前序物品
            JSONObject prevJson = new JSONObject();
            prevJson.put("resourceGroupId", "*");
            prevJson.put("resourceId", "!");
            prevJson.put("dur", DateUtils.second2HourMinuteSecondStr(baseAlgorithmData.getYztSwitchSeconds()));
            detailJson.put("prev", prevJson);
            //后序物品
            JSONObject nextJson = new JSONObject();
            nextJson.put("resourceGroupId", "*");
            nextJson.put("resourceId", resourceId);
            nextJson.put("dur", "00:00:00");
            detailJson.put("next", nextJson);
            switchDurForToolList.add(detailJson);
        }
        return switchDurForToolList;
    }

    protected AlgorithmLog createAmsAlgoLog(AlgorithmLog masterAlgorithmLog, String moduleCode, List<PhysicalResourceVO> resourceVOS) {
        String parentId = null != masterAlgorithmLog ? masterAlgorithmLog.getId() : null;
        String scenario = null != masterAlgorithmLog ? masterAlgorithmLog.getScenario() : SystemHolder.getScenario();
        String userId = null != masterAlgorithmLog ? masterAlgorithmLog.getCreator() : SystemHolder.getUserId();
        String productLine = resourceVOS.stream()
                .filter(t -> ResourceCategoryEnum.MAIN.getCode().equals(t.getResourceCategory()))
                .map(PhysicalResourceVO::getPhysicalResourceCode).collect(Collectors.joining(","));
        productLine = null != masterAlgorithmLog ? masterAlgorithmLog.getProductLine() : productLine;
        log.info("创建AMS算法日志，parentId：{}，场景scenario：{}，创建者：{}", parentId, scenario, userId);
        String ipAddress = ipsNewFeign.getIpAddress(ModuleCodeEnum.AMS.getCode());
        log.info("AMS-ipAddress地址：{}", ipAddress);
        String logId = UUIDUtil.getUUID() + DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR4);
        Date date = new Date();
        AlgorithmLog algorithmLog = new AlgorithmLog();
        algorithmLog.setId(logId);
        algorithmLog.setExecutionNumber(logId);
        algorithmLog.setModuleCode(moduleCode);
        algorithmLog.setParentId(parentId);
        algorithmLog.setIpAddress(ipAddress);
        algorithmLog.setStartTime(date);
        algorithmLog.setFilePath("/usr/local/aps/workspace/" + logId);
        algorithmLog.setModifyTime(date);
        algorithmLog.setCreateTime(date);
        algorithmLog.setScenario(scenario);
        algorithmLog.setCreator(userId);
        algorithmLog.setModifier(userId);
        algorithmLog.setStatus(AlgorithmLogStatusEnum.RUNNING.getCode());
        algorithmLog.setProductLine(productLine);
        CompletableFuture.runAsync(() -> ipsFeign.createAlgorithmLog(algorithmLog));
        log.info("AMS算法日志创建完成，logId：{}", logId);
        return algorithmLog;
    }

    public static void main(String[] args) throws IOException, ClassNotFoundException {
        String filePath = "D:\\ams\\handle9574383b-0195-743ae738-40288188-005820250308132932\\APSInput.ser";
        APSInput apsInput = deserializeObject(filePath);

        System.out.println(apsInput.getConfig());
    }

    public static APSInput deserializeObject(String filePath) throws IOException, ClassNotFoundException {
        try (FileInputStream fileInputStream = new FileInputStream(filePath);
             ObjectInputStream objectInputStream = new ObjectInputStream(fileInputStream)) {
            return (APSInput) objectInputStream.readObject();
        }
    }
}
