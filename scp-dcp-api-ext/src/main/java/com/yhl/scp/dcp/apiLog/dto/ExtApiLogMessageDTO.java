package com.yhl.scp.dcp.apiLog.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ExtApiLogMessageDTO</code>
 * <p>
 * 接口日志报文数据MongoDB文档实体类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-05
 */
@ApiModel(value = "接口日志报文数据DTO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document("ext_api_log_message")
public class ExtApiLogMessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 关联的MySQL日志记录ID
     */
    @ApiModelProperty(value = "关联的MySQL日志记录ID")
    private String logId;

    /**
     * 报文类型（REQUEST/RESPONSE）
     */
    @ApiModelProperty(value = "报文类型（REQUEST/RESPONSE）")
    private String messageType;

    /**
     * 报文数据（JSON格式）
     */
    @ApiModelProperty(value = "报文数据")
    private String messageData;

    /**
     * 数组索引
     */
    @ApiModelProperty(value = "数组索引")
    private Integer arrayIndex;

    /**
     * 数据大小
     */
    @ApiModelProperty(value = "数据大小")
    private Long dataSize;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
