package com.yhl.scp.dcp.apiLog.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dcp.apiLog.dao.ExtApiLogMessageRepository;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogMessageDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogMessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <code>ExtApiLogMessageServiceImpl</code>
 * <p>
 * 接口日志报文数据服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Slf4j
@Service
public class ExtApiLogMessageServiceImpl implements ExtApiLogMessageService {

    @Resource
    private ExtApiLogMessageRepository extApiLogMessageRepository;

    @Override
    public void saveRequestMessage(String logId, String requestBody) {
        if (StringUtils.isNotBlank(requestBody)) {
            processMessageData(logId, requestBody, "REQUEST");
        }
    }

    @Override
    public void saveResponseMessage(String logId, String responseBody) {
        if (StringUtils.isNotBlank(responseBody)) {
            processMessageData(logId, responseBody, "RESPONSE");
        }
    }

    @Override
    public void processMessageData(String logId, String messageData, String messageType) {
        try {
            if (StringUtils.isBlank(messageData)) {
                return;
            }

            if ("RESPONSE".equals(messageType)) {
                // 响应报文特殊处理：提取data.message数组中的对象
                processResponseMessage(logId, messageData);
            } else if ("REQUEST".equals(messageType)) {
                // 请求报文处理：检查是否包含reqCode和分页信息
                processRequestMessage(logId, messageData);
            } else {
                // 其他类型直接存储
                processGenericMessage(logId, messageData, messageType);
            }

            log.debug("处理报文数据成功，logId: {}, messageType: {}", logId, messageType);
        } catch (Exception e) {
            log.error("处理报文数据失败，logId: {}, messageType: {}, error: {}",
                    logId, messageType, e.getMessage());
            // 如果处理失败，直接存储原始数据
            saveMessageItem(logId, messageData, messageType, null);
        }
    }

    /**
     * 保存单个报文项
     *
     * @param logId       日志ID
     * @param messageData 报文数据
     * @param messageType 报文类型
     * @param arrayIndex  数组索引（如果是数组元素）
     */
    private void saveMessageItem(String logId, String messageData, String messageType, Integer arrayIndex) {
        try {
            ExtApiLogMessageDTO messageDTO = ExtApiLogMessageDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .logId(logId)
                    .messageType(messageType)
                    .messageData(messageData)
                    .arrayIndex(arrayIndex)
                    .dataSize((long) messageData.getBytes().length)
                    .createTime(new Date())
                    .build();

            extApiLogMessageRepository.save(messageDTO);
            
            log.debug("保存报文项成功，logId: {}, messageType: {}, arrayIndex: {}", 
                    logId, messageType, arrayIndex);
        } catch (Exception e) {
            log.error("保存报文项失败，logId: {}, messageType: {}, arrayIndex: {}, error: {}", 
                    logId, messageType, arrayIndex, e.getMessage());
        }
    }

    @Override
    public List<ExtApiLogMessageDTO> getMessagesByLogId(String logId) {
        return extApiLogMessageRepository.findByLogId(logId);
    }

    @Override
    public List<ExtApiLogMessageDTO> getMessagesByLogIdAndType(String logId, String messageType) {
        return extApiLogMessageRepository.findByLogIdAndMessageType(logId, messageType);
    }

    @Override
    public void deleteMessagesByLogId(String logId) {
        try {
            extApiLogMessageRepository.deleteByLogId(logId);
            log.debug("删除报文数据成功，logId: {}", logId);
        } catch (Exception e) {
            log.error("删除报文数据失败，logId: {}, error: {}", logId, e.getMessage());
        }
    }
}
