package com.yhl.scp.dcp.apiLog.service;

import com.yhl.scp.dcp.apiLog.service.impl.ExtApiLogMessageServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <code>ExtApiLogMessageServiceTest</code>
 * <p>
 * 接口日志报文服务测试类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Slf4j
public class ExtApiLogMessageServiceTest {

    @Test
    public void testResponseMessageProcessing() {
        // 模拟响应报文
        String responseData = "{\n" +
                "  \"data\": {\n" +
                "    \"status\": \"success\",\n" +
                "    \"responseCode\": 200,\n" +
                "    \"message\": [\n" +
                "      {\n" +
                "        \"companyCode\": \"FYSH\",\n" +
                "        \"ghFlag\": \"N\",\n" +
                "        \"lastUpdateDate\": \"2024-12-23 17:40:09\",\n" +
                "        \"kid\": 43758,\n" +
                "        \"itemCode\": \"00715LFW00001\",\n" +
                "        \"ghTime\": 0,\n" +
                "        \"createUser\": \"020563-王琪\",\n" +
                "        \"plantCode\": \"SJ\",\n" +
                "        \"enableFlag\": \"Y\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"companyCode\": \"FYSH\",\n" +
                "        \"ghFlag\": \"N\",\n" +
                "        \"lastUpdateDate\": \"2024-12-23 17:40:09\",\n" +
                "        \"kid\": 66911,\n" +
                "        \"itemCode\": \"00715TRW00002\",\n" +
                "        \"ghTime\": 0,\n" +
                "        \"createUser\": \"020563-王琪\",\n" +
                "        \"plantCode\": \"SJ\",\n" +
                "        \"enableFlag\": \"Y\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"total\": 2,\n" +
                "    \"totalPage\": 1,\n" +
                "    \"currentPage\": 1,\n" +
                "    \"pageSize\": 10000\n" +
                "  },\n" +
                "  \"code\": 0,\n" +
                "  \"message\": \"success\"\n" +
                "}";

        log.info("原始响应报文：{}", responseData);
        log.info("处理后应该存储两个单独的对象：");
        log.info("对象1：{\"companyCode\":\"FYSH\",\"ghFlag\":\"N\",\"lastUpdateDate\":\"2024-12-23 17:40:09\",\"kid\":43758,\"itemCode\":\"00715LFW00001\",\"ghTime\":0,\"createUser\":\"020563-王琪\",\"plantCode\":\"SJ\",\"enableFlag\":\"Y\"}");
        log.info("对象2：{\"companyCode\":\"FYSH\",\"ghFlag\":\"N\",\"lastUpdateDate\":\"2024-12-23 17:40:09\",\"kid\":66911,\"itemCode\":\"00715TRW00002\",\"ghTime\":0,\"createUser\":\"020563-王琪\",\"plantCode\":\"SJ\",\"enableFlag\":\"Y\"}");
    }

    @Test
    public void testRequestMessageProcessing() {
        // 模拟请求报文
        String requestData = "{\n" +
                "  \"reqCode\": \"queryItems\",\n" +
                "  \"currentPage\": 1,\n" +
                "  \"pageSize\": 10000,\n" +
                "  \"companyCode\": \"FYSH\",\n" +
                "  \"plantCode\": \"SJ\"\n" +
                "}";

        log.info("请求报文：{}", requestData);
        log.info("包含reqCode和分页信息，应该直接存储整个请求对象");
    }
}
